# Generated by Django 4.2.7 on 2025-05-26 19:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pets', '0002_initial'),
        ('messaging', '0003_message_story'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='inquiry_status',
            field=models.CharField(blank=True, choices=[('pending', 'Pending'), ('interested', 'Owner Interested'), ('not_available', 'Not Available'), ('sold', 'Sold'), ('declined', 'Declined')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('regular', 'Regular Message'), ('pet_inquiry', 'Pet Inquiry'), ('pet_response', 'Pet Inquiry Response'), ('story_comment', 'Story Comment')], default='regular', max_length=20),
        ),
        migrations.AddField(
            model_name='message',
            name='pet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inquiry_messages', to='pets.pet'),
        ),
    ]
