# Generated by Django 4.2.7 on 2025-05-26 19:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pets', '0002_initial'),
        ('social', '0004_notification_story_notification_story_comment_text_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='pet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='pets.pet'),
        ),
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('like', 'Like'), ('comment', 'Comment'), ('follow', 'Follow'), ('mention', 'Mention'), ('message', 'Message'), ('story_like', 'Story Like'), ('story_comment', 'Story Comment'), ('pet_inquiry', 'Pet Inquiry'), ('pet_inquiry_response', 'Pet Inquiry Response')], max_length=20),
        ),
    ]
